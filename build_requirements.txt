# Enhanced Build Requirements for WOW Bingo Game
# ==============================================
# This file contains all the packages required for building the game executable
# with the enhanced Nuitka build system

# Core build tools (REQUIRED)
nuitka>=2.7.0
orderedset>=4.1.0
zstandard>=0.21.0

# Essential game dependencies (REQUIRED)
pygame>=2.0.0
pyperclip>=1.8.2

# Performance monitoring (RECOMMENDED)
psutil>=5.9.0

# Image processing (RECOMMENDED for better asset handling)
Pillow>=9.0.0

# Optional database support (OPTIONAL - only if using RethinkDB features)
# rethinkdb>=2.4.0

# Optional GUI framework (OPTIONAL - only for voucher generator)
# kivy>=2.1.0

# Development and testing tools (OPTIONAL)
# pytest>=7.0.0
# pytest-cov>=4.0.0

# Build optimization tools (OPTIONAL but recommended for Windows)
# These help with better executable compression and optimization
# wheel>=0.37.0
# setuptools>=60.0.0

# Note: The enhanced build script will automatically detect which packages
# are available and include only the ones that are installed, avoiding
# the Nuitka crashes caused by missing optional dependencies.
